<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Shop - Premium Beauty Products | Affan Trading Company</title>
  <meta name="description" content="Browse our complete collection of premium beauty products. Fragrances, body care, personal care products and more.">
  
  <!-- Stylesheets -->
  <link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar">
    <div class="container navbar-container">
      <a href="../index.html" class="navbar-brand">
        Affan Trading
      </a>
      
      <ul class="navbar-nav hidden md:flex">
        <li><a href="../index.html" class="nav-link">Home</a></li>
        <li><a href="../index.html#products" class="nav-link">Products</a></li>
        <li><a href="../index.html#about" class="nav-link">About</a></li>
        <li><a href="../index.html#contact" class="nav-link">Contact</a></li>
        <li><a href="shop.html" class="nav-link active">Shop</a></li>
      </ul>
      
      <div class="flex items-center gap-4">
        <div class="search-container relative">
          <input type="text" class="search-input" placeholder="Search products...">
          <div class="search-results"></div>
        </div>
        
        <button class="cart-toggle relative">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"></path>
          </svg>
          <span class="cart-count">0</span>
        </button>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section class="py-20 bg-gradient-primary text-white mt-20">
    <div class="container">
      <div class="text-center">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          Premium Beauty Collection
        </h1>
        <p class="text-xl mb-8 opacity-90 max-w-3xl mx-auto">
          Discover our carefully curated selection of premium beauty products. From luxurious fragrances to essential personal care items, find everything you need to look and feel your best.
        </p>
      </div>
    </div>
  </section>

  <!-- Shop Content -->
  <section class="py-12">
    <div class="container">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Filters Sidebar -->
        <div class="lg:col-span-1">
          <div class="sticky top-24 space-y-6">
            <!-- Search Filter -->
            <div class="card">
              <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">Search Products</h3>
                <input 
                  type="text" 
                  id="product-search" 
                  placeholder="Search by name..." 
                  class="form-input w-full"
                >
              </div>
            </div>

            <!-- Category Filter -->
            <div class="card">
              <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">Categories</h3>
                <div class="space-y-2">
                  <label class="flex items-center">
                    <input type="checkbox" class="category-filter" value="all" checked>
                    <span class="ml-2">All Products</span>
                  </label>
                  <label class="flex items-center">
                    <input type="checkbox" class="category-filter" value="Personal Care">
                    <span class="ml-2">Personal Care</span>
                  </label>
                  <label class="flex items-center">
                    <input type="checkbox" class="category-filter" value="Hair Care">
                    <span class="ml-2">Hair Care</span>
                  </label>
                  <label class="flex items-center">
                    <input type="checkbox" class="category-filter" value="Body Care">
                    <span class="ml-2">Body Care</span>
                  </label>
                  <label class="flex items-center">
                    <input type="checkbox" class="category-filter" value="Fragrance">
                    <span class="ml-2">Fragrance</span>
                  </label>
                  <label class="flex items-center">
                    <input type="checkbox" class="category-filter" value="Premium Fragrance">
                    <span class="ml-2">Premium Fragrance</span>
                  </label>
                </div>
              </div>
            </div>

            <!-- Brand Filter -->
            <div class="card">
              <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">Brands</h3>
                <div class="space-y-2">
                  <label class="flex items-center">
                    <input type="checkbox" class="brand-filter" value="Affan Premium">
                    <span class="ml-2">Affan Premium</span>
                  </label>
                  <label class="flex items-center">
                    <input type="checkbox" class="brand-filter" value="Daise">
                    <span class="ml-2">Daise</span>
                  </label>
                  <label class="flex items-center">
                    <input type="checkbox" class="brand-filter" value="Fine'ry">
                    <span class="ml-2">Fine'ry</span>
                  </label>
                  <label class="flex items-center">
                    <input type="checkbox" class="brand-filter" value="XO KHLOE">
                    <span class="ml-2">XO KHLOE</span>
                  </label>
                </div>
              </div>
            </div>

            <!-- Price Filter -->
            <div class="card">
              <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">Price Range</h3>
                <div class="space-y-2">
                  <label class="flex items-center">
                    <input type="checkbox" class="price-filter" value="0-20">
                    <span class="ml-2">$0 - $20</span>
                  </label>
                  <label class="flex items-center">
                    <input type="checkbox" class="price-filter" value="20-40">
                    <span class="ml-2">$20 - $40</span>
                  </label>
                  <label class="flex items-center">
                    <input type="checkbox" class="price-filter" value="40-60">
                    <span class="ml-2">$40 - $60</span>
                  </label>
                  <label class="flex items-center">
                    <input type="checkbox" class="price-filter" value="60+">
                    <span class="ml-2">$60+</span>
                  </label>
                </div>
              </div>
            </div>

            <!-- Clear Filters -->
            <button id="clear-filters" class="btn btn-ghost w-full">
              Clear All Filters
            </button>
          </div>
        </div>

        <!-- Products Grid -->
        <div class="lg:col-span-3">
          <!-- Sort and View Options -->
          <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
            <div>
              <p class="text-gray-600">
                Showing <span id="product-count">13</span> products
              </p>
            </div>
            
            <div class="flex items-center gap-4">
              <label class="text-sm font-medium text-gray-700">Sort by:</label>
              <select id="sort-select" class="form-input">
                <option value="name">Name A-Z</option>
                <option value="name-desc">Name Z-A</option>
                <option value="price">Price Low to High</option>
                <option value="price-desc">Price High to Low</option>
                <option value="rating">Highest Rated</option>
                <option value="newest">Newest First</option>
              </select>
            </div>
          </div>

          <!-- Products Grid -->
          <div id="products-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Products will be loaded here by JavaScript -->
          </div>

          <!-- Load More Button -->
          <div class="text-center mt-12">
            <button id="load-more" class="btn btn-primary btn-lg hover-lift" style="display: none;">
              Load More Products
            </button>
          </div>

          <!-- No Results Message -->
          <div id="no-results" class="text-center py-12" style="display: none;">
            <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33"></path>
            </svg>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">No products found</h3>
            <p class="text-gray-600">Try adjusting your filters or search terms</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Newsletter Section -->
  <section class="py-20 bg-gradient-secondary text-white">
    <div class="container">
      <div class="max-w-4xl mx-auto text-center">
        <h2 class="text-4xl md:text-5xl font-bold mb-6">
          Never Miss a Deal
        </h2>
        <p class="text-xl mb-8 opacity-90">
          Subscribe to our newsletter for exclusive offers, new product launches, and beauty tips.
        </p>
        
        <form class="max-w-md mx-auto">
          <div class="flex flex-col sm:flex-row gap-4">
            <input 
              type="email" 
              placeholder="Enter your email address" 
              class="flex-1 px-6 py-4 rounded-lg text-gray-900 focus:outline-none focus:ring-4 focus:ring-white focus:ring-opacity-30"
              required
            >
            <button 
              type="submit" 
              class="btn bg-white text-secondary-color hover:bg-gray-100 px-8 py-4 font-semibold"
            >
              Subscribe
            </button>
          </div>
        </form>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gray-900 text-white py-16">
    <div class="container">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
        <div class="md:col-span-2">
          <h3 class="text-2xl font-bold mb-4">Affan Trading Company</h3>
          <p class="text-gray-300 mb-6 leading-relaxed">
            Your trusted source for premium beauty products. We bring you the finest selection of fragrances, body care, and personal care products from Austin, TX to Pakistan.
          </p>
        </div>

        <div>
          <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
          <ul class="space-y-2">
            <li><a href="../index.html" class="text-gray-300 hover:text-white transition-colors">Home</a></li>
            <li><a href="../index.html#products" class="text-gray-300 hover:text-white transition-colors">Products</a></li>
            <li><a href="../index.html#about" class="text-gray-300 hover:text-white transition-colors">About Us</a></li>
            <li><a href="../index.html#contact" class="text-gray-300 hover:text-white transition-colors">Contact</a></li>
          </ul>
        </div>

        <div>
          <h4 class="text-lg font-semibold mb-4">Customer Service</h4>
          <ul class="space-y-2">
            <li><a href="faq.html" class="text-gray-300 hover:text-white transition-colors">FAQ</a></li>
            <li><a href="shipping.html" class="text-gray-300 hover:text-white transition-colors">Shipping Info</a></li>
            <li><a href="returns.html" class="text-gray-300 hover:text-white transition-colors">Returns</a></li>
            <li><a href="privacy.html" class="text-gray-300 hover:text-white transition-colors">Privacy Policy</a></li>
          </ul>
        </div>
      </div>

      <div class="border-t border-gray-800 pt-8">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <p class="text-gray-400 text-sm">
            &copy; 2025 Affan Trading Company. All rights reserved.
          </p>
          <p class="text-gray-400 text-sm mt-2 md:mt-0">
            Made with ❤️ in Austin, TX
          </p>
        </div>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="../data/products.js"></script>
  <script src="../assets/js/main.js"></script>
  <script src="../assets/js/shop.js"></script>
</body>
</html>
