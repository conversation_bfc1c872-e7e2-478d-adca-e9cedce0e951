<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Shopping Cart - Affan Trading Company</title>
  <meta name="description" content="Review your selected beauty products and proceed to checkout.">
  
  <!-- Stylesheets -->
  <link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar">
    <div class="container navbar-container">
      <a href="../index.html" class="navbar-brand">
        Affan Trading
      </a>
      
      <ul class="navbar-nav hidden md:flex">
        <li><a href="../index.html" class="nav-link">Home</a></li>
        <li><a href="../index.html#products" class="nav-link">Products</a></li>
        <li><a href="../index.html#about" class="nav-link">About</a></li>
        <li><a href="../index.html#contact" class="nav-link">Contact</a></li>
        <li><a href="shop.html" class="nav-link">Shop</a></li>
      </ul>
      
      <div class="flex items-center gap-4">
        <button class="cart-toggle relative">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"></path>
          </svg>
          <span class="cart-count">0</span>
        </button>
      </div>
    </div>
  </nav>

  <!-- Breadcrumb -->
  <section class="py-4 bg-gray-50 mt-20">
    <div class="container">
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-2">
          <li>
            <a href="../index.html" class="text-gray-500 hover:text-primary-color">Home</a>
          </li>
          <li>
            <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
          </li>
          <li>
            <span class="text-gray-900 font-medium">Shopping Cart</span>
          </li>
        </ol>
      </nav>
    </div>
  </section>

  <!-- Cart Content -->
  <section class="py-12">
    <div class="container">
      <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-8">Shopping Cart</h1>
      
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Cart Items -->
        <div class="lg:col-span-2">
          <div id="cart-items" class="space-y-6">
            <!-- Cart items will be loaded here -->
          </div>
          
          <!-- Empty Cart Message -->
          <div id="empty-cart" class="text-center py-12">
            <svg class="w-24 h-24 text-gray-300 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"></path>
            </svg>
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">Your cart is empty</h2>
            <p class="text-gray-600 mb-8">Looks like you haven't added any items to your cart yet.</p>
            <a href="shop.html" class="btn btn-primary btn-lg hover-lift">
              Continue Shopping
            </a>
          </div>
        </div>

        <!-- Order Summary -->
        <div class="lg:col-span-1">
          <div class="card sticky top-24">
            <div class="card-header">
              <h2 class="text-xl font-semibold">Order Summary</h2>
            </div>
            <div class="card-body space-y-4">
              <div class="flex justify-between">
                <span class="text-gray-600">Subtotal</span>
                <span id="subtotal" class="font-semibold">$0.00</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Shipping</span>
                <span id="shipping" class="font-semibold">$12.99</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Tax</span>
                <span id="tax" class="font-semibold">$0.00</span>
              </div>
              <hr>
              <div class="flex justify-between text-lg font-bold">
                <span>Total</span>
                <span id="total" class="text-primary-color">$0.00</span>
              </div>
              
              <!-- Promo Code -->
              <div class="pt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Promo Code</label>
                <div class="flex gap-2">
                  <input type="text" id="promo-code" placeholder="Enter code" class="form-input flex-1">
                  <button id="apply-promo" class="btn btn-ghost btn-sm">Apply</button>
                </div>
              </div>
              
              <!-- Checkout Button -->
              <button id="checkout-btn" class="btn btn-primary w-full btn-lg hover-lift mt-6" disabled>
                Proceed to Checkout
              </button>
              
              <!-- Continue Shopping -->
              <a href="shop.html" class="btn btn-ghost w-full text-center">
                Continue Shopping
              </a>
            </div>
          </div>

          <!-- Trust Badges -->
          <div class="mt-8 text-center">
            <h3 class="text-lg font-semibold mb-4">Secure Checkout</h3>
            <div class="flex justify-center items-center gap-4 text-gray-400">
              <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
              </svg>
              <span class="text-sm">SSL Secured</span>
            </div>
            <p class="text-xs text-gray-500 mt-2">Your information is protected by 256-bit SSL encryption</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Recommended Products -->
  <section class="py-16 bg-gray-50">
    <div class="container">
      <h2 class="text-3xl font-bold text-center mb-12">You Might Also Like</h2>
      <div id="recommended-products" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8">
        <!-- Recommended products will be loaded here -->
      </div>
    </div>
  </section>

  <!-- Scripts -->
  <script src="../data/products.js"></script>
  <script src="../assets/js/main.js"></script>
  <script>
    class CartPage {
      constructor() {
        this.cart = JSON.parse(localStorage.getItem('affan-cart')) || [];
        this.products = []; // This would be loaded from products.js
        this.init();
      }

      async init() {
        await this.loadProducts();
        this.renderCart();
        this.setupEventListeners();
        this.loadRecommendedProducts();
      }

      async loadProducts() {
        // Sample product data - in real app, this would come from API
        this.products = [
          { id: '1', name: 'Antibacterial Soap, 2 Count', price: 19.99, image: '../images/product1.jpg' },
          { id: '6', name: "Fine'ry Body Mist - Sweet On the Outside", price: 29.99, image: '../images/product6.jpg' },
          { id: '12', name: 'XO KHLOE Eau de Parfum', price: 89.99, image: '../images/product12.jpg' }
        ];
      }

      renderCart() {
        const cartItemsContainer = document.getElementById('cart-items');
        const emptyCartMessage = document.getElementById('empty-cart');
        const checkoutBtn = document.getElementById('checkout-btn');

        if (this.cart.length === 0) {
          cartItemsContainer.style.display = 'none';
          emptyCartMessage.style.display = 'block';
          checkoutBtn.disabled = true;
          return;
        }

        cartItemsContainer.style.display = 'block';
        emptyCartMessage.style.display = 'none';
        checkoutBtn.disabled = false;

        const cartHTML = this.cart.map(item => {
          const product = this.products.find(p => p.id === item.id);
          if (!product) return '';

          return `
            <div class="cart-item bg-white rounded-lg p-6 shadow-md">
              <div class="flex items-center gap-6">
                <img src="${product.image}" alt="${product.name}" class="w-20 h-20 object-cover rounded-lg">
                <div class="flex-1">
                  <h3 class="font-semibold text-gray-900 mb-2">${product.name}</h3>
                  <p class="text-gray-600 text-sm mb-2">In Stock</p>
                  <div class="flex items-center gap-4">
                    <div class="flex items-center border border-gray-300 rounded-lg">
                      <button class="px-3 py-1 text-gray-600 hover:text-gray-800" onclick="cartPage.updateQuantity('${item.id}', ${item.quantity - 1})">-</button>
                      <span class="px-3 py-1 border-l border-r border-gray-300">${item.quantity}</span>
                      <button class="px-3 py-1 text-gray-600 hover:text-gray-800" onclick="cartPage.updateQuantity('${item.id}', ${item.quantity + 1})">+</button>
                    </div>
                    <button class="text-red-600 hover:text-red-800 text-sm" onclick="cartPage.removeItem('${item.id}')">Remove</button>
                  </div>
                </div>
                <div class="text-right">
                  <p class="text-lg font-semibold text-gray-900">$${(product.price * item.quantity).toFixed(2)}</p>
                  <p class="text-sm text-gray-600">$${product.price} each</p>
                </div>
              </div>
            </div>
          `;
        }).join('');

        cartItemsContainer.innerHTML = cartHTML;
        this.updateOrderSummary();
      }

      updateQuantity(productId, newQuantity) {
        if (newQuantity <= 0) {
          this.removeItem(productId);
          return;
        }

        const item = this.cart.find(item => item.id === productId);
        if (item) {
          item.quantity = newQuantity;
          this.saveCart();
          this.renderCart();
        }
      }

      removeItem(productId) {
        this.cart = this.cart.filter(item => item.id !== productId);
        this.saveCart();
        this.renderCart();
        this.updateCartCount();
      }

      saveCart() {
        localStorage.setItem('affan-cart', JSON.stringify(this.cart));
      }

      updateCartCount() {
        const cartCount = document.querySelector('.cart-count');
        const totalItems = this.cart.reduce((sum, item) => sum + item.quantity, 0);
        if (cartCount) {
          cartCount.textContent = totalItems;
          cartCount.style.display = totalItems > 0 ? 'block' : 'none';
        }
      }

      updateOrderSummary() {
        const subtotal = this.cart.reduce((sum, item) => {
          const product = this.products.find(p => p.id === item.id);
          return sum + (product ? product.price * item.quantity : 0);
        }, 0);

        const shipping = subtotal > 75 ? 0 : 12.99;
        const tax = subtotal * 0.08; // 8% tax
        const total = subtotal + shipping + tax;

        document.getElementById('subtotal').textContent = `$${subtotal.toFixed(2)}`;
        document.getElementById('shipping').textContent = shipping === 0 ? 'FREE' : `$${shipping.toFixed(2)}`;
        document.getElementById('tax').textContent = `$${tax.toFixed(2)}`;
        document.getElementById('total').textContent = `$${total.toFixed(2)}`;
      }

      setupEventListeners() {
        const applyPromoBtn = document.getElementById('apply-promo');
        const checkoutBtn = document.getElementById('checkout-btn');

        if (applyPromoBtn) {
          applyPromoBtn.addEventListener('click', () => {
            const promoCode = document.getElementById('promo-code').value;
            this.applyPromoCode(promoCode);
          });
        }

        if (checkoutBtn) {
          checkoutBtn.addEventListener('click', () => {
            this.proceedToCheckout();
          });
        }
      }

      applyPromoCode(code) {
        // Simple promo code logic
        const validCodes = {
          'WELCOME10': 0.1,
          'SAVE20': 0.2,
          'FIRST15': 0.15
        };

        if (validCodes[code.toUpperCase()]) {
          alert(`Promo code applied! ${(validCodes[code.toUpperCase()] * 100)}% discount`);
        } else {
          alert('Invalid promo code');
        }
      }

      proceedToCheckout() {
        alert('Checkout functionality would be implemented here. This would redirect to a secure payment processor.');
      }

      loadRecommendedProducts() {
        const recommendedContainer = document.getElementById('recommended-products');
        const recommendedProducts = this.products.slice(0, 4);

        const recommendedHTML = recommendedProducts.map(product => `
          <div class="product-card hover-lift">
            <div class="product-image">
              <img src="${product.image}" alt="${product.name}" class="w-full h-48 object-cover rounded-lg">
            </div>
            <div class="p-4">
              <h3 class="font-semibold text-gray-900 mb-2">${product.name}</h3>
              <p class="text-primary-color font-bold">$${product.price}</p>
              <button class="btn btn-primary btn-sm w-full mt-3" onclick="cartPage.addToCart('${product.id}')">
                Add to Cart
              </button>
            </div>
          </div>
        `).join('');

        recommendedContainer.innerHTML = recommendedHTML;
      }

      addToCart(productId) {
        const existingItem = this.cart.find(item => item.id === productId);
        
        if (existingItem) {
          existingItem.quantity += 1;
        } else {
          this.cart.push({ id: productId, quantity: 1 });
        }

        this.saveCart();
        this.renderCart();
        this.updateCartCount();
        this.showNotification('Product added to cart!', 'success');
      }

      showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
          notification.classList.add('show');
        }, 100);
        
        setTimeout(() => {
          notification.classList.remove('show');
          setTimeout(() => {
            if (document.body.contains(notification)) {
              document.body.removeChild(notification);
            }
          }, 300);
        }, 3000);
      }
    }

    // Initialize cart page
    let cartPage;
    document.addEventListener('DOMContentLoaded', () => {
      cartPage = new CartPage();
    });
  </script>
</body>
</html>
