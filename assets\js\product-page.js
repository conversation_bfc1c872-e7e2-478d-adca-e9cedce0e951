// Product Page JavaScript
class ProductPage {
    constructor() {
        this.currentQuantity = 1;
        this.init();
    }

    init() {
        this.setupTabs();
        this.setupQuantityControls();
        this.setupImageGallery();
        this.loadProductReviews();
    }

    // Tab functionality
    setupTabs() {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabPanes = document.querySelectorAll('.tab-pane');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.dataset.tab;
                
                // Remove active class from all buttons and panes
                tabButtons.forEach(btn => {
                    btn.classList.remove('active', 'border-primary-color', 'text-primary-color');
                    btn.classList.add('border-transparent', 'text-gray-500');
                });
                
                tabPanes.forEach(pane => {
                    pane.classList.add('hidden');
                    pane.classList.remove('active');
                });

                // Add active class to clicked button and corresponding pane
                button.classList.add('active', 'border-primary-color', 'text-primary-color');
                button.classList.remove('border-transparent', 'text-gray-500');
                
                const targetPane = document.getElementById(targetTab);
                if (targetPane) {
                    targetPane.classList.remove('hidden');
                    targetPane.classList.add('active');
                }
            });
        });
    }

    // Quantity controls
    setupQuantityControls() {
        const quantityInput = document.getElementById('quantity');
        
        if (quantityInput) {
            quantityInput.addEventListener('change', (e) => {
                this.currentQuantity = parseInt(e.target.value) || 1;
                if (this.currentQuantity < 1) {
                    this.currentQuantity = 1;
                    quantityInput.value = 1;
                }
            });
        }
    }

    // Image gallery functionality
    setupImageGallery() {
        const thumbnails = document.querySelectorAll('.thumbnail-btn');
        const mainImage = document.getElementById('main-product-image');

        thumbnails.forEach(thumbnail => {
            thumbnail.addEventListener('click', () => {
                const newImageSrc = thumbnail.querySelector('img').src;
                
                // Remove active class from all thumbnails
                thumbnails.forEach(thumb => {
                    thumb.classList.remove('border-primary-color');
                    thumb.classList.add('border-gray-200');
                });
                
                // Add active class to clicked thumbnail
                thumbnail.classList.add('border-primary-color');
                thumbnail.classList.remove('border-gray-200');
                
                // Update main image with fade effect
                if (mainImage) {
                    mainImage.style.opacity = '0.5';
                    setTimeout(() => {
                        mainImage.src = newImageSrc;
                        mainImage.style.opacity = '1';
                    }, 150);
                }
            });
        });
    }

    // Load and display product reviews
    loadProductReviews() {
        const reviewsContainer = document.getElementById('reviews');
        if (!reviewsContainer) return;

        // Sample reviews data - in a real app, this would come from an API
        const sampleReviews = [
            {
                id: 1,
                name: "Sarah Johnson",
                rating: 5,
                date: "2025-01-15",
                title: "Amazing product!",
                comment: "This product exceeded my expectations. The quality is outstanding and the fragrance lasts all day. Highly recommend!",
                verified: true
            },
            {
                id: 2,
                name: "Maria Garcia",
                rating: 4,
                date: "2025-01-10",
                title: "Great value for money",
                comment: "Really good product for the price. Fast shipping and excellent packaging. Will definitely order again.",
                verified: true
            },
            {
                id: 3,
                name: "Ayesha Khan",
                rating: 5,
                date: "2025-01-05",
                title: "Love it!",
                comment: "Perfect product! Exactly as described and arrived quickly. The customer service was also excellent.",
                verified: false
            }
        ];

        this.renderReviews(sampleReviews);
    }

    renderReviews(reviews) {
        const reviewsContainer = document.getElementById('reviews');
        if (!reviewsContainer) return;

        const reviewsHTML = reviews.map(review => `
            <div class="border-b border-gray-200 pb-6 last:border-b-0">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center text-white font-semibold mr-3">
                            ${review.name.charAt(0)}
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">${review.name}</h4>
                            <div class="flex items-center gap-2">
                                <div class="stars">
                                    ${this.generateStars(review.rating)}
                                </div>
                                ${review.verified ? '<span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Verified Purchase</span>' : ''}
                            </div>
                        </div>
                    </div>
                    <span class="text-sm text-gray-500">${this.formatDate(review.date)}</span>
                </div>
                <h5 class="font-medium text-gray-900 mb-2">${review.title}</h5>
                <p class="text-gray-700 leading-relaxed">${review.comment}</p>
            </div>
        `).join('');

        reviewsContainer.innerHTML = `
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4">Customer Reviews</h3>
                <div class="bg-gray-50 rounded-lg p-6 mb-6">
                    <div class="flex items-center gap-4 mb-4">
                        <div class="text-3xl font-bold text-primary-color">4.8</div>
                        <div>
                            <div class="stars text-lg">
                                ★★★★★
                            </div>
                            <p class="text-sm text-gray-600">Based on ${reviews.length} reviews</p>
                        </div>
                    </div>
                    <button class="btn btn-primary btn-sm">Write a Review</button>
                </div>
            </div>
            <div class="space-y-6">
                ${reviewsHTML}
            </div>
        `;
    }

    generateStars(rating) {
        let stars = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                stars += '<span class="star">★</span>';
            } else {
                stars += '<span class="star text-gray-300">★</span>';
            }
        }
        return stars;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        });
    }
}

// Quantity control functions (global scope for inline onclick handlers)
function increaseQuantity() {
    const quantityInput = document.getElementById('quantity');
    if (quantityInput) {
        const currentValue = parseInt(quantityInput.value) || 1;
        quantityInput.value = currentValue + 1;
        quantityInput.dispatchEvent(new Event('change'));
    }
}

function decreaseQuantity() {
    const quantityInput = document.getElementById('quantity');
    if (quantityInput) {
        const currentValue = parseInt(quantityInput.value) || 1;
        if (currentValue > 1) {
            quantityInput.value = currentValue - 1;
            quantityInput.dispatchEvent(new Event('change'));
        }
    }
}

function addToCart(productId) {
    const quantityInput = document.getElementById('quantity');
    const quantity = quantityInput ? parseInt(quantityInput.value) || 1 : 1;
    
    // Add to cart logic (this would integrate with the main cart system)
    if (window.AffanTradingApp) {
        for (let i = 0; i < quantity; i++) {
            window.AffanTradingApp.prototype.addToCart.call({
                cart: JSON.parse(localStorage.getItem('affan-cart')) || [],
                saveCart: function() {
                    localStorage.setItem('affan-cart', JSON.stringify(this.cart));
                },
                updateCartUI: function() {
                    const cartCount = document.querySelector('.cart-count');
                    const totalItems = this.cart.reduce((sum, item) => sum + item.quantity, 0);
                    if (cartCount) {
                        cartCount.textContent = totalItems;
                        cartCount.style.display = totalItems > 0 ? 'block' : 'none';
                    }
                }
            }, productId);
        }
    }
    
    // Show success notification
    showNotification(`Added ${quantity} item(s) to cart!`, 'success');
}

function toggleWishlist(productId) {
    // Wishlist logic (this would integrate with the main wishlist system)
    let wishlist = JSON.parse(localStorage.getItem('affan-wishlist')) || [];
    
    if (wishlist.includes(productId)) {
        wishlist = wishlist.filter(id => id !== productId);
        showNotification('Removed from wishlist', 'info');
    } else {
        wishlist.push(productId);
        showNotification('Added to wishlist', 'success');
    }
    
    localStorage.setItem('affan-wishlist', JSON.stringify(wishlist));
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Initialize product page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ProductPage();
});
