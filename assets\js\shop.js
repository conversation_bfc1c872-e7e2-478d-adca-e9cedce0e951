// Shop Page JavaScript
class ShopPage {
    constructor() {
        this.allProducts = [];
        this.filteredProducts = [];
        this.displayedProducts = [];
        this.productsPerPage = 9;
        this.currentPage = 1;
        this.filters = {
            search: '',
            categories: [],
            brands: [],
            priceRanges: []
        };
        this.sortBy = 'name';
        
        this.init();
    }

    async init() {
        await this.loadProducts();
        this.setupEventListeners();
        this.renderProducts();
        this.updateProductCount();
    }

    async loadProducts() {
        // Use the products from the global products array if available
        if (typeof products !== 'undefined' && products.length > 0) {
            this.allProducts = products;
            this.filteredProducts = [...this.allProducts];
            return;
        }

        // Fallback to sample product data
        this.allProducts = [
            {
                id: 1,
                name: "Antibacterial Soap, 2 Count",
                shortDescription: "Long lasting deodorant protection with premium antibacterial formula.",
                price: 19.99,
                originalPrice: 24.99,
                category: "Personal Care",
                brand: "Affan Premium",
                image: "../images/product1.jpg",
                rating: 4.5,
                reviews: 127,
                inStock: true
            },
            {
                id: 2,
                name: "No Lye Conditioning Crème Relaxer for Gray Hair",
                shortDescription: "Maintains elegance and manageability of gray hair with gentle formula.",
                price: 18.99,
                originalPrice: 22.99,
                category: "Hair Care",
                brand: "Affan Professional",
                image: "../images/product2.jpg",
                rating: 4.3,
                reviews: 89,
                inStock: true
            },
            {
                id: 4,
                name: "Daise Hello Full Body Deodorant Spray - 3.5oz",
                shortDescription: "Luxurious full-body deodorant spray with amber and fruit notes.",
                price: 16.99,
                originalPrice: 19.99,
                category: "Body Care",
                brand: "Daise",
                image: "../images/product4.jpg",
                rating: 4.4,
                reviews: 156,
                inStock: true
            },
            {
                id: 5,
                name: "Daise Peachy Full Body Deodorant Spray - 3.5oz",
                shortDescription: "Refreshing full-body deodorant spray with berry and citrus notes.",
                price: 16.99,
                originalPrice: 19.99,
                category: "Body Care",
                brand: "Daise",
                image: "../images/product5.jpg",
                rating: 4.6,
                reviews: 203,
                inStock: true
            },
            {
                id: 6,
                name: "Fine'ry Body Mist Fragrance Spray - Sweet On the Outside - 5 fl oz",
                shortDescription: "Madagascar Vanilla, Cacao Wood, Smoked Vetiver luxury fragrance.",
                price: 29.99,
                originalPrice: 34.99,
                category: "Fragrance",
                brand: "Fine'ry",
                image: "../images/product6.jpg",
                rating: 4.8,
                reviews: 312,
                inStock: true
            },
            {
                id: 7,
                name: "Fine'ry Women's Pistachio Please Body Mist, 5 Fl Oz",
                shortDescription: "Unique and captivating body mist with distinctive pistachio fragrance.",
                price: 29.99,
                originalPrice: 34.99,
                category: "Fragrance",
                brand: "Fine'ry",
                image: "../images/product7.jpg",
                rating: 4.5,
                reviews: 189,
                inStock: true
            },
            {
                id: 8,
                name: "Fine'ry Without a Trace Body Mist, Long Lasting Scent, 5 fl oz",
                shortDescription: "Mysterious and captivating body mist with subtle yet memorable scent.",
                price: 29.99,
                originalPrice: 34.99,
                category: "Fragrance",
                brand: "Fine'ry",
                image: "../images/product8.jpg",
                rating: 4.7,
                reviews: 267,
                inStock: true
            },
            {
                id: 9,
                name: "Daise Happy Full Body Deodorant Spray - 3.5oz",
                shortDescription: "Uplifting full-body deodorant spray with joyful amber and fruit blend.",
                price: 16.99,
                originalPrice: 19.99,
                category: "Body Care",
                brand: "Daise",
                image: "../images/product9.jpg",
                rating: 4.4,
                reviews: 178,
                inStock: true
            },
            {
                id: 10,
                name: "Daise Sunny Full Body Deodorant Spray - 3.5oz",
                shortDescription: "Tropical-inspired deodorant spray with warm coconut and vanilla notes.",
                price: 16.99,
                originalPrice: 19.99,
                category: "Body Care",
                brand: "Daise",
                image: "../images/product10.jpg",
                rating: 4.6,
                reviews: 234,
                inStock: true
            },
            {
                id: 11,
                name: "Daise Happy Full Body Deodorant Spray - 3.5oz (Enhanced)",
                shortDescription: "Enhanced version with improved longevity and whole body coverage.",
                price: 16.99,
                originalPrice: 19.99,
                category: "Body Care",
                brand: "Daise",
                image: "../images/product11.jpg",
                rating: 4.5,
                reviews: 145,
                inStock: true
            },
            {
                id: 12,
                name: "XO KHLOE Eau de Parfum Spray, Floral Woody Fragrance for Women, Long-Lasting Scent, 30 ml",
                shortDescription: "Luxurious floral woody fragrance for the sophisticated woman.",
                price: 89.99,
                originalPrice: 109.99,
                category: "Premium Fragrance",
                brand: "XO KHLOE",
                image: "../images/product12.jpg",
                rating: 4.9,
                reviews: 89,
                inStock: true
            },
            {
                id: 13,
                name: "XO KHLOE Parfum Travel Spray - Floral Woody Eau de Parfum - 0.33 fl oz / 10 ml",
                shortDescription: "Exquisite travel-size parfum with complex floral woody blend.",
                price: 49.99,
                originalPrice: 59.99,
                category: "Premium Fragrance",
                brand: "XO KHLOE",
                image: "../images/product13.jpg",
                rating: 4.8,
                reviews: 156,
                inStock: true
            }
        ];

        this.filteredProducts = [...this.allProducts];
    }

    setupEventListeners() {
        // Search input
        const searchInput = document.getElementById('product-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filters.search = e.target.value.toLowerCase();
                this.applyFilters();
            });
        }

        // Category filters
        const categoryFilters = document.querySelectorAll('.category-filter');
        categoryFilters.forEach(filter => {
            filter.addEventListener('change', () => {
                this.updateCategoryFilters();
                this.applyFilters();
            });
        });

        // Brand filters
        const brandFilters = document.querySelectorAll('.brand-filter');
        brandFilters.forEach(filter => {
            filter.addEventListener('change', () => {
                this.updateBrandFilters();
                this.applyFilters();
            });
        });

        // Price filters
        const priceFilters = document.querySelectorAll('.price-filter');
        priceFilters.forEach(filter => {
            filter.addEventListener('change', () => {
                this.updatePriceFilters();
                this.applyFilters();
            });
        });

        // Sort select
        const sortSelect = document.getElementById('sort-select');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.sortBy = e.target.value;
                this.sortProducts();
                this.renderProducts();
            });
        }

        // Clear filters
        const clearFiltersBtn = document.getElementById('clear-filters');
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => {
                this.clearAllFilters();
            });
        }

        // Load more button
        const loadMoreBtn = document.getElementById('load-more');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreProducts();
            });
        }
    }

    updateCategoryFilters() {
        const categoryFilters = document.querySelectorAll('.category-filter');
        this.filters.categories = [];
        
        categoryFilters.forEach(filter => {
            if (filter.checked && filter.value !== 'all') {
                this.filters.categories.push(filter.value);
            }
        });
    }

    updateBrandFilters() {
        const brandFilters = document.querySelectorAll('.brand-filter');
        this.filters.brands = [];
        
        brandFilters.forEach(filter => {
            if (filter.checked) {
                this.filters.brands.push(filter.value);
            }
        });
    }

    updatePriceFilters() {
        const priceFilters = document.querySelectorAll('.price-filter');
        this.filters.priceRanges = [];
        
        priceFilters.forEach(filter => {
            if (filter.checked) {
                this.filters.priceRanges.push(filter.value);
            }
        });
    }

    applyFilters() {
        this.filteredProducts = this.allProducts.filter(product => {
            // Search filter
            if (this.filters.search && !product.name.toLowerCase().includes(this.filters.search)) {
                return false;
            }

            // Category filter
            if (this.filters.categories.length > 0 && !this.filters.categories.includes(product.category)) {
                return false;
            }

            // Brand filter
            if (this.filters.brands.length > 0 && !this.filters.brands.includes(product.brand)) {
                return false;
            }

            // Price filter
            if (this.filters.priceRanges.length > 0) {
                const price = product.price;
                let matchesPrice = false;
                
                this.filters.priceRanges.forEach(range => {
                    if (range === '0-20' && price <= 20) matchesPrice = true;
                    if (range === '20-40' && price > 20 && price <= 40) matchesPrice = true;
                    if (range === '40-60' && price > 40 && price <= 60) matchesPrice = true;
                    if (range === '60+' && price > 60) matchesPrice = true;
                });
                
                if (!matchesPrice) return false;
            }

            return true;
        });

        this.currentPage = 1;
        this.sortProducts();
        this.renderProducts();
        this.updateProductCount();
    }

    sortProducts() {
        this.filteredProducts.sort((a, b) => {
            switch (this.sortBy) {
                case 'name':
                    return a.name.localeCompare(b.name);
                case 'name-desc':
                    return b.name.localeCompare(a.name);
                case 'price':
                    return a.price - b.price;
                case 'price-desc':
                    return b.price - a.price;
                case 'rating':
                    return b.rating - a.rating;
                case 'newest':
                    return b.id - a.id;
                default:
                    return 0;
            }
        });
    }

    renderProducts() {
        const productsGrid = document.getElementById('products-grid');
        const noResults = document.getElementById('no-results');
        const loadMoreBtn = document.getElementById('load-more');

        if (this.filteredProducts.length === 0) {
            productsGrid.innerHTML = '';
            noResults.style.display = 'block';
            loadMoreBtn.style.display = 'none';
            return;
        }

        noResults.style.display = 'none';

        const startIndex = 0;
        const endIndex = this.currentPage * this.productsPerPage;
        this.displayedProducts = this.filteredProducts.slice(startIndex, endIndex);

        productsGrid.innerHTML = this.displayedProducts.map(product => this.createProductCard(product)).join('');

        // Show/hide load more button
        if (endIndex >= this.filteredProducts.length) {
            loadMoreBtn.style.display = 'none';
        } else {
            loadMoreBtn.style.display = 'block';
        }
    }

    createProductCard(product) {
        const savings = (product.originalPrice - product.price).toFixed(2);
        const stars = this.generateStars(product.rating);
        
        return `
            <div class="product-card scroll-reveal hover-lift" data-product-id="${product.id}">
                <div class="product-image">
                    <img src="${product.image}" alt="${product.name}" loading="lazy">
                    ${savings > 0 ? `<div class="product-badge">Save $${savings}</div>` : ''}
                    <div class="product-overlay">
                        <button class="btn btn-primary btn-sm add-to-cart" onclick="addToCart('${product.id}')">Add to Cart</button>
                        <button class="btn btn-ghost btn-sm add-to-wishlist" onclick="toggleWishlist('${product.id}')">♡</button>
                    </div>
                </div>
                <div class="product-info">
                    <h3 class="product-title">
                        <a href="product-${product.id}.html" class="hover:text-primary-color transition-colors">
                            ${product.name}
                        </a>
                    </h3>
                    <p class="product-description">${product.shortDescription}</p>
                    <div class="product-rating">
                        <div class="stars">${stars}</div>
                        <span class="rating-text">(${product.reviews} reviews)</span>
                    </div>
                    <div class="product-price">
                        <span class="price-current">$${product.price}</span>
                        ${product.originalPrice > product.price ? `<span class="price-original">$${product.originalPrice}</span>` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    generateStars(rating) {
        let stars = '';
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;

        for (let i = 1; i <= 5; i++) {
            if (i <= fullStars) {
                stars += '<span class="star">★</span>';
            } else if (i === fullStars + 1 && hasHalfStar) {
                stars += '<span class="star">☆</span>';
            } else {
                stars += '<span class="star text-gray-300">☆</span>';
            }
        }
        return stars;
    }

    loadMoreProducts() {
        this.currentPage++;
        this.renderProducts();
    }

    updateProductCount() {
        const productCount = document.getElementById('product-count');
        if (productCount) {
            productCount.textContent = this.filteredProducts.length;
        }
    }

    clearAllFilters() {
        // Clear search
        const searchInput = document.getElementById('product-search');
        if (searchInput) searchInput.value = '';

        // Clear all checkboxes
        const allFilters = document.querySelectorAll('.category-filter, .brand-filter, .price-filter');
        allFilters.forEach(filter => {
            filter.checked = false;
        });

        // Check "All Products" category
        const allProductsFilter = document.querySelector('.category-filter[value="all"]');
        if (allProductsFilter) allProductsFilter.checked = true;

        // Reset filters
        this.filters = {
            search: '',
            categories: [],
            brands: [],
            priceRanges: []
        };

        this.applyFilters();
    }
}

// Global functions for product interactions
function addToCart(productId) {
    // This would integrate with the main cart system
    console.log('Adding product to cart:', productId);
    showNotification('Product added to cart!', 'success');
}

function toggleWishlist(productId) {
    // This would integrate with the main wishlist system
    console.log('Toggling wishlist for product:', productId);

    let wishlist = JSON.parse(localStorage.getItem('affan-wishlist')) || [];

    if (wishlist.includes(productId)) {
        wishlist = wishlist.filter(id => id !== productId);
        showNotification('Removed from wishlist', 'info');
    } else {
        wishlist.push(productId);
        showNotification('Added to wishlist!', 'success');
    }

    localStorage.setItem('affan-wishlist', JSON.stringify(wishlist));
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Initialize shop page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ShopPage();
});
