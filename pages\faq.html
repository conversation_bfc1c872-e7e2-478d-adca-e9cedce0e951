<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>FAQ - Frequently Asked Questions | Affan Trading Company</title>
  <meta name="description" content="Find answers to frequently asked questions about our products, shipping, returns, and more.">
  
  <!-- Stylesheets -->
  <link rel="stylesheet" href="../assets/css/main.css">
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar">
    <div class="container navbar-container">
      <a href="../index.html" class="navbar-brand">
        Affan Trading
      </a>
      
      <ul class="navbar-nav hidden md:flex">
        <li><a href="../index.html" class="nav-link">Home</a></li>
        <li><a href="../index.html#products" class="nav-link">Products</a></li>
        <li><a href="../index.html#about" class="nav-link">About</a></li>
        <li><a href="../index.html#contact" class="nav-link">Contact</a></li>
        <li><a href="shop.html" class="nav-link">Shop</a></li>
      </ul>
      
      <div class="flex items-center gap-4">
        <button class="cart-toggle relative">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"></path>
          </svg>
          <span class="cart-count">0</span>
        </button>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section class="py-20 bg-gradient-primary text-white mt-20">
    <div class="container">
      <div class="text-center">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          Frequently Asked Questions
        </h1>
        <p class="text-xl mb-8 opacity-90 max-w-3xl mx-auto">
          Find answers to common questions about our products, shipping, returns, and more. Can't find what you're looking for? Contact us directly.
        </p>
      </div>
    </div>
  </section>

  <!-- FAQ Content -->
  <section class="py-16">
    <div class="container">
      <div class="max-w-4xl mx-auto">
        <!-- FAQ Categories -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          <div class="text-center">
            <div class="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2">Products</h3>
            <p class="text-gray-600">Questions about our beauty products and their usage</p>
          </div>
          
          <div class="text-center">
            <div class="w-16 h-16 bg-gradient-secondary rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2">Shipping</h3>
            <p class="text-gray-600">Information about delivery times and shipping costs</p>
          </div>
          
          <div class="text-center">
            <div class="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2">Returns</h3>
            <p class="text-gray-600">Our return policy and how to process returns</p>
          </div>
        </div>

        <!-- FAQ Items -->
        <div class="space-y-6">
          <!-- Product Questions -->
          <div class="faq-item">
            <button class="faq-question w-full text-left p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">Are your products authentic?</h3>
                <svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </button>
            <div class="faq-answer hidden p-6 bg-gray-50 rounded-b-lg">
              <p class="text-gray-700">Yes, absolutely! All our products are 100% authentic and sourced directly from authorized distributors. We guarantee the authenticity of every item we sell and provide certificates of authenticity when available.</p>
            </div>
          </div>

          <div class="faq-item">
            <button class="faq-question w-full text-left p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">How long do fragrances typically last?</h3>
                <svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </button>
            <div class="faq-answer hidden p-6 bg-gray-50 rounded-b-lg">
              <p class="text-gray-700">The longevity of fragrances varies by product type and individual skin chemistry. Body mists typically last 2-4 hours, while eau de parfums can last 6-8 hours or more. For best results, apply to pulse points and moisturized skin.</p>
            </div>
          </div>

          <div class="faq-item">
            <button class="faq-question w-full text-left p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">Do you offer samples or travel sizes?</h3>
                <svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </button>
            <div class="faq-answer hidden p-6 bg-gray-50 rounded-b-lg">
              <p class="text-gray-700">Yes! We offer travel-size versions of many popular products, including the XO KHLOE travel spray. We also occasionally include samples with orders. Check individual product pages for available sizes.</p>
            </div>
          </div>

          <!-- Shipping Questions -->
          <div class="faq-item">
            <button class="faq-question w-full text-left p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">How long does shipping to Pakistan take?</h3>
                <svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </button>
            <div class="faq-answer hidden p-6 bg-gray-50 rounded-b-lg">
              <p class="text-gray-700">Standard shipping from Austin, TX to Pakistan typically takes 7-14 business days. Express shipping options are available for 3-7 business days. Delivery times may vary during peak seasons or due to customs processing.</p>
            </div>
          </div>

          <div class="faq-item">
            <button class="faq-question w-full text-left p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">What are the shipping costs?</h3>
                <svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </button>
            <div class="faq-answer hidden p-6 bg-gray-50 rounded-b-lg">
              <p class="text-gray-700">Shipping costs vary based on order value and shipping method. We offer free standard shipping on orders over $75. Standard shipping rates start at $12.99, while express shipping starts at $24.99. Exact costs are calculated at checkout.</p>
            </div>
          </div>

          <div class="faq-item">
            <button class="faq-question w-full text-left p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">Do you handle customs and duties?</h3>
                <svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </button>
            <div class="faq-answer hidden p-6 bg-gray-50 rounded-b-lg">
              <p class="text-gray-700">We handle all customs documentation and declare appropriate values. However, import duties and taxes are the responsibility of the recipient and vary by country. We recommend checking with local customs authorities for specific rates.</p>
            </div>
          </div>

          <!-- Return Questions -->
          <div class="faq-item">
            <button class="faq-question w-full text-left p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">What is your return policy?</h3>
                <svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </button>
            <div class="faq-answer hidden p-6 bg-gray-50 rounded-b-lg">
              <p class="text-gray-700">We offer a 30-day return policy for unopened items in original packaging. Due to hygiene reasons, opened beauty products cannot be returned unless defective. Return shipping costs are the customer's responsibility unless the item was damaged or incorrect.</p>
            </div>
          </div>

          <div class="faq-item">
            <button class="faq-question w-full text-left p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">How do I initiate a return?</h3>
                <svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </button>
            <div class="faq-answer hidden p-6 bg-gray-50 rounded-b-lg">
              <p class="text-gray-700">To initiate a return, contact our customer service <NAME_EMAIL> with your order number and reason for return. We'll provide you with return instructions and a return authorization number within 24 hours.</p>
            </div>
          </div>

          <!-- General Questions -->
          <div class="faq-item">
            <button class="faq-question w-full text-left p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">How can I track my order?</h3>
                <svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </button>
            <div class="faq-answer hidden p-6 bg-gray-50 rounded-b-lg">
              <p class="text-gray-700">Once your order ships, you'll receive a tracking number via email. You can use this number to track your package on the carrier's website. We also send updates at key delivery milestones.</p>
            </div>
          </div>

          <div class="faq-item">
            <button class="faq-question w-full text-left p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">Do you offer customer support in Urdu?</h3>
                <svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </button>
            <div class="faq-answer hidden p-6 bg-gray-50 rounded-b-lg">
              <p class="text-gray-700">Yes! Our customer support team includes Urdu speakers who can assist you in your preferred language. Simply mention your language preference when contacting us, and we'll connect you with the appropriate team member.</p>
            </div>
          </div>
        </div>

        <!-- Contact Section -->
        <div class="mt-16 text-center">
          <div class="bg-gradient-primary text-white rounded-2xl p-8">
            <h2 class="text-2xl font-bold mb-4">Still have questions?</h2>
            <p class="text-lg mb-6 opacity-90">
              Our customer service team is here to help you with any questions or concerns.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <a href="../index.html#contact" class="btn bg-white text-primary-color hover:bg-gray-100">
                Contact Us
              </a>
              <a href="mailto:<EMAIL>" class="btn btn-ghost border-white text-white hover:bg-white hover:text-primary-color">
                Email Support
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Scripts -->
  <script src="../assets/js/main.js"></script>
  <script>
    // FAQ Accordion functionality
    document.addEventListener('DOMContentLoaded', function() {
      const faqItems = document.querySelectorAll('.faq-item');
      
      faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');
        const icon = question.querySelector('svg');
        
        question.addEventListener('click', () => {
          const isOpen = !answer.classList.contains('hidden');
          
          // Close all other items
          faqItems.forEach(otherItem => {
            const otherAnswer = otherItem.querySelector('.faq-answer');
            const otherIcon = otherItem.querySelector('.faq-question svg');
            otherAnswer.classList.add('hidden');
            otherIcon.style.transform = 'rotate(0deg)';
          });
          
          // Toggle current item
          if (!isOpen) {
            answer.classList.remove('hidden');
            icon.style.transform = 'rotate(180deg)';
          }
        });
      });
    });
  </script>
</body>
</html>
