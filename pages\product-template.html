<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{PRODUCT_NAME}} - Affan Trading Company</title>
  <meta name="description" content="{{PRODUCT_DESCRIPTION}} - Premium beauty products from Affan Trading Company">
  
  <!-- Stylesheets -->
  <link rel="stylesheet" href="../assets/css/main.css">
  
  <!-- Structured Data for SEO -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org/",
    "@type": "Product",
    "name": "{{PRODUCT_NAME}}",
    "image": "../{{PRODUCT_IMAGE}}",
    "description": "{{PRODUCT_DESCRIPTION}}",
    "brand": {
      "@type": "Brand",
      "name": "{{PRODUCT_BRAND}}"
    },
    "offers": {
      "@type": "Offer",
      "url": "{{PRODUCT_URL}}",
      "priceCurrency": "USD",
      "price": "{{PRODUCT_PRICE}}",
      "availability": "https://schema.org/InStock"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "{{PRODUCT_RATING}}",
      "reviewCount": "{{PRODUCT_REVIEWS}}"
    }
  }
  </script>
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar">
    <div class="container navbar-container">
      <a href="../index.html" class="navbar-brand">
        Affan Trading
      </a>
      
      <ul class="navbar-nav hidden md:flex">
        <li><a href="../index.html" class="nav-link">Home</a></li>
        <li><a href="../index.html#products" class="nav-link">Products</a></li>
        <li><a href="../index.html#about" class="nav-link">About</a></li>
        <li><a href="../index.html#contact" class="nav-link">Contact</a></li>
        <li><a href="shop.html" class="nav-link">Shop</a></li>
      </ul>
      
      <div class="flex items-center gap-4">
        <div class="search-container relative">
          <input type="text" class="search-input" placeholder="Search products...">
          <div class="search-results"></div>
        </div>
        
        <button class="cart-toggle relative">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"></path>
          </svg>
          <span class="cart-count">0</span>
        </button>
      </div>
    </div>
  </nav>

  <!-- Breadcrumb -->
  <section class="py-4 bg-gray-50 mt-20">
    <div class="container">
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-2">
          <li>
            <a href="../index.html" class="text-gray-500 hover:text-primary-color">Home</a>
          </li>
          <li>
            <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
          </li>
          <li>
            <a href="shop.html" class="text-gray-500 hover:text-primary-color">Products</a>
          </li>
          <li>
            <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
          </li>
          <li>
            <span class="text-gray-900 font-medium">{{PRODUCT_NAME}}</span>
          </li>
        </ol>
      </nav>
    </div>
  </section>

  <!-- Product Details -->
  <section class="py-12">
    <div class="container">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <!-- Product Images -->
        <div class="space-y-4">
          <div class="aspect-square overflow-hidden rounded-2xl bg-gray-100">
            <img 
              src="../{{PRODUCT_IMAGE}}" 
              alt="{{PRODUCT_NAME}}" 
              class="w-full h-full object-cover hover:scale-110 transition-transform duration-500"
              id="main-product-image"
            >
          </div>
          
          <!-- Thumbnail Gallery -->
          <div class="grid grid-cols-4 gap-4">
            <button class="aspect-square overflow-hidden rounded-lg bg-gray-100 border-2 border-primary-color">
              <img src="../{{PRODUCT_IMAGE}}" alt="{{PRODUCT_NAME}}" class="w-full h-full object-cover">
            </button>
            <!-- Additional thumbnails would be added here -->
          </div>
        </div>

        <!-- Product Information -->
        <div class="space-y-6">
          <div>
            <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-2">{{PRODUCT_NAME}}</h1>
            <p class="text-lg text-gray-600">{{PRODUCT_BRAND}}</p>
          </div>

          <!-- Rating -->
          <div class="flex items-center gap-4">
            <div class="flex items-center">
              <div class="stars">
                {{PRODUCT_STARS}}
              </div>
              <span class="ml-2 text-sm text-gray-600">({{PRODUCT_REVIEWS}} reviews)</span>
            </div>
          </div>

          <!-- Price -->
          <div class="flex items-center gap-4">
            <span class="text-3xl font-bold text-primary-color">${{PRODUCT_PRICE}}</span>
            <span class="text-xl text-gray-500 line-through">${{PRODUCT_ORIGINAL_PRICE}}</span>
            <span class="bg-red-100 text-red-800 text-sm font-medium px-2 py-1 rounded">
              Save ${{PRODUCT_SAVINGS}}
            </span>
          </div>

          <!-- Short Description -->
          <p class="text-lg text-gray-700 leading-relaxed">
            {{PRODUCT_SHORT_DESCRIPTION}}
          </p>

          <!-- Add to Cart Section -->
          <div class="space-y-4">
            <div class="flex items-center gap-4">
              <label class="text-sm font-medium text-gray-700">Quantity:</label>
              <div class="flex items-center border border-gray-300 rounded-lg">
                <button class="px-3 py-2 text-gray-600 hover:text-gray-800" onclick="decreaseQuantity()">-</button>
                <input type="number" id="quantity" value="1" min="1" class="w-16 text-center border-0 focus:ring-0">
                <button class="px-3 py-2 text-gray-600 hover:text-gray-800" onclick="increaseQuantity()">+</button>
              </div>
            </div>

            <div class="flex flex-col sm:flex-row gap-4">
              <button class="btn btn-primary btn-lg flex-1 hover-lift" onclick="addToCart('{{PRODUCT_ID}}')">
                Add to Cart
                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"></path>
                </svg>
              </button>
              <button class="btn btn-ghost btn-lg hover-scale" onclick="toggleWishlist('{{PRODUCT_ID}}')">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
                Add to Wishlist
              </button>
            </div>
          </div>

          <!-- Product Features -->
          <div class="border-t pt-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Key Features</h3>
            <ul class="space-y-2">
              {{PRODUCT_FEATURES}}
            </ul>
          </div>

          <!-- Share -->
          <div class="border-t pt-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Share this product</h3>
            <div class="flex gap-4">
              <button class="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center hover:bg-blue-700">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                </svg>
              </button>
              <button class="w-10 h-10 bg-blue-800 text-white rounded-full flex items-center justify-center hover:bg-blue-900">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                </svg>
              </button>
              <button class="w-10 h-10 bg-pink-600 text-white rounded-full flex items-center justify-center hover:bg-pink-700">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z.017 0z"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Product Details Tabs -->
  <section class="py-12 bg-gray-50">
    <div class="container">
      <div class="max-w-4xl mx-auto">
        <!-- Tab Navigation -->
        <div class="border-b border-gray-200 mb-8">
          <nav class="flex space-x-8">
            <button class="tab-btn active py-4 px-1 border-b-2 border-primary-color text-primary-color font-medium" data-tab="description">
              Description
            </button>
            <button class="tab-btn py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="ingredients">
              Ingredients
            </button>
            <button class="tab-btn py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="usage">
              How to Use
            </button>
            <button class="tab-btn py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="reviews">
              Reviews ({{PRODUCT_REVIEWS}})
            </button>
          </nav>
        </div>

        <!-- Tab Content -->
        <div class="tab-content">
          <!-- Description Tab -->
          <div id="description" class="tab-pane active">
            <div class="prose max-w-none">
              <p class="text-lg text-gray-700 leading-relaxed">
                {{PRODUCT_FULL_DESCRIPTION}}
              </p>
            </div>
          </div>

          <!-- Ingredients Tab -->
          <div id="ingredients" class="tab-pane hidden">
            <div class="prose max-w-none">
              <h3 class="text-xl font-semibold mb-4">Ingredients</h3>
              <p class="text-gray-700">{{PRODUCT_INGREDIENTS}}</p>
            </div>
          </div>

          <!-- Usage Tab -->
          <div id="usage" class="tab-pane hidden">
            <div class="prose max-w-none">
              <h3 class="text-xl font-semibold mb-4">How to Use</h3>
              <p class="text-gray-700">{{PRODUCT_USAGE}}</p>
            </div>
          </div>

          <!-- Reviews Tab -->
          <div id="reviews" class="tab-pane hidden">
            <div class="space-y-6">
              {{PRODUCT_REVIEW_CONTENT}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Scripts -->
  <script src="../data/products.js"></script>
  <script src="../assets/js/main.js"></script>
  <script src="../assets/js/product-page.js"></script>
</body>
</html>
