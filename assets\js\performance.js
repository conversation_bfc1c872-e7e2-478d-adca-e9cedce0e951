// Performance Optimization Script for Affan Trading Company
class PerformanceOptimizer {
    constructor() {
        this.init();
    }

    init() {
        this.setupLazyLoading();
        this.setupImageOptimization();
        this.setupCriticalResourcePreloading();
        this.setupServiceWorker();
        this.setupAnalytics();
    }

    // Lazy loading for images
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        } else {
            // Fallback for browsers without IntersectionObserver
            document.querySelectorAll('img[data-src]').forEach(img => {
                img.src = img.dataset.src;
            });
        }
    }

    // Image optimization and WebP support
    setupImageOptimization() {
        // Check for WebP support
        const supportsWebP = () => {
            const canvas = document.createElement('canvas');
            canvas.width = 1;
            canvas.height = 1;
            return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
        };

        if (supportsWebP()) {
            document.documentElement.classList.add('webp');
        } else {
            document.documentElement.classList.add('no-webp');
        }

        // Responsive images based on device pixel ratio
        const updateImageSources = () => {
            const pixelRatio = window.devicePixelRatio || 1;
            const images = document.querySelectorAll('img[data-src-2x]');
            
            images.forEach(img => {
                if (pixelRatio > 1 && img.dataset.src2x) {
                    img.src = img.dataset.src2x;
                }
            });
        };

        updateImageSources();
    }

    // Preload critical resources
    setupCriticalResourcePreloading() {
        const criticalResources = [
            { href: 'assets/css/main.css', as: 'style' },
            { href: 'assets/js/main.js', as: 'script' },
            { href: 'data/products.js', as: 'script' }
        ];

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource.href;
            link.as = resource.as;
            document.head.appendChild(link);
        });
    }

    // Service Worker for caching
    setupServiceWorker() {
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    }

    // Analytics and performance monitoring
    setupAnalytics() {
        // Web Vitals monitoring
        if ('PerformanceObserver' in window) {
            // Largest Contentful Paint
            new PerformanceObserver((entryList) => {
                for (const entry of entryList.getEntries()) {
                    console.log('LCP:', entry.startTime);
                }
            }).observe({ entryTypes: ['largest-contentful-paint'] });

            // First Input Delay
            new PerformanceObserver((entryList) => {
                for (const entry of entryList.getEntries()) {
                    console.log('FID:', entry.processingStart - entry.startTime);
                }
            }).observe({ entryTypes: ['first-input'] });

            // Cumulative Layout Shift
            new PerformanceObserver((entryList) => {
                for (const entry of entryList.getEntries()) {
                    if (!entry.hadRecentInput) {
                        console.log('CLS:', entry.value);
                    }
                }
            }).observe({ entryTypes: ['layout-shift'] });
        }

        // Page load time
        window.addEventListener('load', () => {
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            console.log('Page load time:', loadTime + 'ms');
        });
    }

    // Optimize animations for performance
    static optimizeAnimations() {
        // Reduce animations for users who prefer reduced motion
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            document.documentElement.style.setProperty('--duration-fast', '0ms');
            document.documentElement.style.setProperty('--duration-base', '0ms');
            document.documentElement.style.setProperty('--duration-slow', '0ms');
        }

        // Use transform and opacity for animations (GPU accelerated)
        const animatedElements = document.querySelectorAll('.animate-fade-in-up, .animate-scale-in');
        animatedElements.forEach(el => {
            el.style.willChange = 'transform, opacity';
        });
    }

    // Memory management
    static cleanupEventListeners() {
        // Remove event listeners when elements are removed from DOM
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.removedNodes.forEach((node) => {
                    if (node.nodeType === 1) { // Element node
                        // Clean up any event listeners or timers associated with removed elements
                        const elements = node.querySelectorAll('[data-cleanup]');
                        elements.forEach(el => {
                            if (el._cleanup) {
                                el._cleanup();
                            }
                        });
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Bundle size optimization
    static async loadModuleOnDemand(moduleName) {
        try {
            const module = await import(`./modules/${moduleName}.js`);
            return module;
        } catch (error) {
            console.error(`Failed to load module ${moduleName}:`, error);
        }
    }

    // Critical CSS inlining
    static inlineCriticalCSS() {
        const criticalCSS = `
            /* Critical CSS for above-the-fold content */
            .navbar { position: fixed; top: 0; left: 0; right: 0; z-index: 1030; }
            .hero { min-height: 100vh; display: flex; align-items: center; }
            .btn-primary { background: linear-gradient(135deg, #ff6b9d 0%, #4ecdc4 100%); }
        `;

        const style = document.createElement('style');
        style.textContent = criticalCSS;
        document.head.insertBefore(style, document.head.firstChild);
    }

    // Resource hints
    static addResourceHints() {
        const hints = [
            { rel: 'dns-prefetch', href: '//fonts.googleapis.com' },
            { rel: 'dns-prefetch', href: '//fonts.gstatic.com' },
            { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
            { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: true }
        ];

        hints.forEach(hint => {
            const link = document.createElement('link');
            Object.keys(hint).forEach(key => {
                if (key === 'crossorigin') {
                    link.crossOrigin = hint[key];
                } else {
                    link[key] = hint[key];
                }
            });
            document.head.appendChild(link);
        });
    }
}

// Initialize performance optimizations
document.addEventListener('DOMContentLoaded', () => {
    new PerformanceOptimizer();
    PerformanceOptimizer.optimizeAnimations();
    PerformanceOptimizer.cleanupEventListeners();
    PerformanceOptimizer.addResourceHints();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceOptimizer;
}
