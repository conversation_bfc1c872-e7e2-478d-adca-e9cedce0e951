// Product Page Generator
// This script generates individual product pages from the template

class ProductPageGenerator {
    constructor() {
        this.template = '';
        this.products = [];
        this.init();
    }

    async init() {
        try {
            await this.loadTemplate();
            await this.loadProducts();
            this.generateAllPages();
        } catch (error) {
            console.error('Error generating product pages:', error);
        }
    }

    async loadTemplate() {
        try {
            const response = await fetch('../pages/product-template.html');
            this.template = await response.text();
        } catch (error) {
            console.error('Error loading template:', error);
        }
    }

    async loadProducts() {
        // In a real application, this would load from the products.js file
        // For now, we'll use the same product data structure
        this.products = [
            {
                id: 1,
                name: "Antibacterial Soap, 2 Count",
                shortDescription: "Long lasting deodorant protection with premium antibacterial formula.",
                fullDescription: "Premium antibacterial soap with long-lasting deodorant protection. This gentle yet effective formula cleanses and protects your skin while providing all-day freshness. Perfect for daily use, this soap contains natural antibacterial agents that help eliminate germs and bacteria while maintaining your skin's natural moisture balance. The 2-count pack ensures you always have a backup ready.",
                price: 19.99,
                originalPrice: 24.99,
                category: "Personal Care",
                brand: "Affan Premium",
                image: "images/product1.jpg",
                features: [
                    "Long-lasting deodorant protection",
                    "Antibacterial formula",
                    "Gentle on skin",
                    "Natural ingredients",
                    "2-count pack"
                ],
                ingredients: "Sodium Palmate, Sodium Palm Kernelate, Aqua, Glycerin, Triclosan, Fragrance",
                usage: "Wet hands, apply soap, lather for 20 seconds, rinse thoroughly",
                rating: 4.5,
                reviews: 127
            },
            {
                id: 6,
                name: "Fine'ry Body Mist Fragrance Spray - Sweet On the Outside - 5 fl oz",
                shortDescription: "Madagascar Vanilla, Cacao Wood, Smoked Vetiver luxury fragrance.",
                fullDescription: "Indulgent body mist featuring an exquisite blend of Madagascar vanilla, rich cacao wood, and sophisticated smoked vetiver. This luxurious fragrance creates a warm, inviting aura that's perfect for any occasion. The long-lasting formula ensures your signature scent stays with you throughout the day. Each spray delivers a perfect balance of sweetness and sophistication.",
                price: 29.99,
                originalPrice: 34.99,
                category: "Fragrance",
                brand: "Fine'ry",
                image: "images/product6.jpg",
                features: [
                    "Madagascar vanilla notes",
                    "Cacao wood & smoked vetiver",
                    "5 fl oz generous size",
                    "Long-lasting fragrance",
                    "Luxurious scent profile"
                ],
                ingredients: "Alcohol Denat., Aqua, Parfum, Benzyl Salicylate, Coumarin, Vanillin",
                usage: "Spray on pulse points and body for lasting fragrance. Hold 6 inches away from skin.",
                rating: 4.8,
                reviews: 312
            },
            {
                id: 12,
                name: "XO KHLOE Eau de Parfum Spray, Floral Woody Fragrance for Women, Long-Lasting Scent, 30 ml",
                shortDescription: "Luxurious floral woody fragrance for the sophisticated woman.",
                fullDescription: "Luxurious eau de parfum featuring a sophisticated floral woody composition. This premium fragrance combines delicate floral notes with rich woody undertones, creating an elegant and timeless scent. Perfect for the modern woman who appreciates refined luxury and lasting quality. The 30ml bottle is perfect for daily use and special occasions.",
                price: 89.99,
                originalPrice: 109.99,
                category: "Premium Fragrance",
                brand: "XO KHLOE",
                image: "images/product12.jpg",
                features: [
                    "Premium eau de parfum",
                    "Floral woody composition",
                    "Long-lasting formula",
                    "Elegant 30ml bottle",
                    "Luxury fragrance experience"
                ],
                ingredients: "Alcohol Denat., Parfum, Aqua, Benzyl Salicylate, Linalool, Geraniol, Citronellol",
                usage: "Apply to pulse points for optimal fragrance projection and longevity. Use sparingly for best results.",
                rating: 4.9,
                reviews: 89
            }
        ];
    }

    generateAllPages() {
        this.products.forEach(product => {
            this.generateProductPage(product);
        });
    }

    generateProductPage(product) {
        let pageContent = this.template;

        // Replace all placeholders
        const replacements = {
            '{{PRODUCT_ID}}': product.id,
            '{{PRODUCT_NAME}}': product.name,
            '{{PRODUCT_DESCRIPTION}}': product.shortDescription,
            '{{PRODUCT_SHORT_DESCRIPTION}}': product.shortDescription,
            '{{PRODUCT_FULL_DESCRIPTION}}': product.fullDescription,
            '{{PRODUCT_BRAND}}': product.brand,
            '{{PRODUCT_IMAGE}}': product.image,
            '{{PRODUCT_PRICE}}': product.price,
            '{{PRODUCT_ORIGINAL_PRICE}}': product.originalPrice,
            '{{PRODUCT_SAVINGS}}': (product.originalPrice - product.price).toFixed(2),
            '{{PRODUCT_RATING}}': product.rating,
            '{{PRODUCT_REVIEWS}}': product.reviews,
            '{{PRODUCT_STARS}}': this.generateStars(product.rating),
            '{{PRODUCT_FEATURES}}': this.generateFeaturesList(product.features),
            '{{PRODUCT_INGREDIENTS}}': product.ingredients,
            '{{PRODUCT_USAGE}}': product.usage,
            '{{PRODUCT_URL}}': `https://maffantrading.com/pages/product-${product.id}.html`,
            '{{PRODUCT_REVIEW_CONTENT}}': this.generateReviewContent(product)
        };

        // Replace all placeholders in the template
        Object.keys(replacements).forEach(placeholder => {
            const regex = new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g');
            pageContent = pageContent.replace(regex, replacements[placeholder]);
        });

        // Save the generated page (in a real environment, this would write to file system)
        this.saveProductPage(product.id, pageContent);
    }

    generateStars(rating) {
        let stars = '';
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;

        for (let i = 1; i <= 5; i++) {
            if (i <= fullStars) {
                stars += '<span class="star">★</span>';
            } else if (i === fullStars + 1 && hasHalfStar) {
                stars += '<span class="star">☆</span>';
            } else {
                stars += '<span class="star text-gray-300">☆</span>';
            }
        }
        return stars;
    }

    generateFeaturesList(features) {
        return features.map(feature => `
            <li class="flex items-start">
                <svg class="w-5 h-5 text-primary-color mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-700">${feature}</span>
            </li>
        `).join('');
    }

    generateReviewContent(product) {
        // This would typically load real reviews from a database
        return `
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4">Customer Reviews</h3>
                <div class="bg-gray-50 rounded-lg p-6 mb-6">
                    <div class="flex items-center gap-4 mb-4">
                        <div class="text-3xl font-bold text-primary-color">${product.rating}</div>
                        <div>
                            <div class="stars text-lg">
                                ${this.generateStars(product.rating)}
                            </div>
                            <p class="text-sm text-gray-600">Based on ${product.reviews} reviews</p>
                        </div>
                    </div>
                    <button class="btn btn-primary btn-sm">Write a Review</button>
                </div>
            </div>
        `;
    }

    saveProductPage(productId, content) {
        // In a real environment, this would save to the file system
        // For demonstration, we'll log the filename and show it's ready
        const filename = `product-${productId}.html`;
        console.log(`Generated product page: ${filename}`);
        
        // Create a downloadable blob for demonstration
        const blob = new Blob([content], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        
        // Create download link (for demonstration purposes)
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';
        document.body.appendChild(link);
        
        // Auto-download (commented out to avoid spam)
        // link.click();
        
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }
}

// Manual page generation function
function generateProductPages() {
    new ProductPageGenerator();
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProductPageGenerator;
}
