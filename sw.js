// Service Worker for Affan Trading Company
const CACHE_NAME = 'affan-trading-v1';
const urlsToCache = [
  '/',
  '/index.html',
  '/assets/css/main.css',
  '/assets/js/main.js',
  '/assets/js/performance.js',
  '/data/products.js',
  '/pages/shop.html',
  '/pages/faq.html',
  '/pages/cart.html',
  '/images/product1.jpg',
  '/images/product2.jpg',
  '/images/product3.jpg',
  '/images/product4.jpg',
  '/images/product5.jpg',
  '/images/product6.jpg',
  '/images/product7.jpg',
  '/images/product8.jpg',
  '/images/product9.jpg',
  '/images/product10.jpg',
  '/images/product11.jpg',
  '/images/product12.jpg',
  '/images/product13.jpg'
];

// Install event - cache resources
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Opened cache');
        return cache.addAll(urlsToCache);
      })
  );
});

// Fetch event - serve from cache when offline
self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        if (response) {
          return response;
        }
        return fetch(event.request);
      }
    )
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});
